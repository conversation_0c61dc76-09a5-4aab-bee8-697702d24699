# ==============================================================
# 最小構成 pyproject.toml
# 必要に応じて group.dev に lint / test 依存を追加してください

[tool.poetry]
name = "code-indexer"
version = "0.1.0"
description = "Local codebase vector indexer (Cursor-like)"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"
packages = [{include = "indexer"}, {include = "scripts"}]

[tool.poetry.dependencies]
python = "^3.10"
# 向量数据库和搜索
faiss-gpu = "^1.7.2"
sentence-transformers = "^2.7.0"
rapidfuzz = "^3.6.1"
# 文件监控和模式匹配 (与主工程版本保持一致)
watchdog = "^4.0.0"
pathspec = "0.12.1"
# Web框架 (与主工程版本保持一致)
fastapi = "0.115.12"
uvicorn = "0.34.0"
pydantic = "2.11.1"
pydantic-settings = "2.8.0"
# HTTP客户端 (与主工程版本保持一致)
httpx = "0.28.1"
requests = "*"
# LangChain相关 (与主工程版本保持一致)
langchain = "0.3.23"
langchain-community = "0.3.21"
langchain-huggingface = "0.1.2"
# 注意: RAG服务通过相对导入使用，不需要在依赖中声明

[tool.poetry.group.dev.dependencies]
# テスト関連 (与主工程版本保持一致)
pytest = ">=8.3.5"
pytest-cov = ">=6.1.1"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"


