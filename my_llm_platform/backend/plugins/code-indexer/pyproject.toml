# ==============================================================
# 最小構成 pyproject.toml
# 必要に応じて group.dev に lint / test 依存を追加してください

[tool.poetry]
name = "code-indexer"
version = "0.1.0"
description = "Local codebase vector indexer (Cursor-like)"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.10"
faiss-cpu = "^1.7.4"
watchdog = "^4.0.0"
sentence-transformers = "^2.7.0"
pathspec = "^0.12.1"
rapidfuzz = "^3.6.1"
rag_service = {path = "./rag_service", develop = true}

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"


