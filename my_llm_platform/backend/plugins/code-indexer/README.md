# ==============================================================
"""# code-indexer

Cursor 風ローカルコードベース索引サービス。

## Quick Start
```bash
# 依存インストール
poetry install

# フルインデックス作成
poetry run python scripts/index_cli.py init  --path ./your_repo \
                                          --index ./faiss.index
# 常時監視 (Ctrl+C で停止)
poetry run python scripts/index_cli.py watch --path ./your_repo \
                                            --index ./faiss.index
# 検索 (ベクトル近傍 ID を取得)
poetry run python scripts/index_cli.py query --index ./faiss.index "エントリーポイントは？"
```

## ライセンス
MIT
"""


### README.md

---
# Code‑Indexer
_Local codebase embedding & incremental indexing service_

## ✨ Features
| 機能 / Feature | 説明 / Description |
|----------------|--------------------|
| **Incremental Merkle Scan** | Detects file changes with SHA‑256 Merkle tree, only re‑indexes deltas |
| **Chunk‑Aware Embedding** | Re‑uses `rag_service.DocumentLoader` so chunking rules stay identical |
| **Faiss Hybrid Index** | Automatically picks `IndexFlatL2` (<100k vecs) or `IndexIVFFlat+OPQ` (>100k) |
| **Watchdog Realtime Update** | File change events trigger `RAGService.add_document()` within <1 s |
| **CLI & Service Mode** | `code-indexer {init,update,query,info}` and systemd / NSSM service wrappers |

## 🏗️ Installation
```bash
# 1. clone & install
git clone https://example.com/code-indexer.git
cd code-indexer
poetry install

# 2. configure env (zsh/bash)
export INDEX_ROOT=$HOME/your_repo
export FAISS_INDEX=$HOME/.cache/code_index.faiss
export EMBEDDING_MODEL=thenlper/gte-base  # or keep rag_service default
```

## 🚀 Quick Start
```bash
# 初期索引
poetry run code-indexer init --path $INDEX_ROOT

# バックグラウンド監視 (Linux user service)
cp deploy/code-indexer.service ~/.config/systemd/user/
systemctl --user daemon-reload
systemctl --user enable --now code-indexer

# 查询
poetry run code-indexer query "ユーザー認証ロジックは？"
```

## 🔧 Configuration Options
| ENV / CLI | Default | Note |
|-----------|---------|------|
| `INDEX_ROOT` | `pwd` | target repository path |
| `FAISS_INDEX` | `.faiss` under repo | absolute path recommended for services |
| `EMBEDDING_MODEL` | `thenlper/gte-base` | must equal rag_service VectorStore model |
| `MAX_WORKERS` | `8` | file I/O thread pool size |

## 🩹 Troubleshooting
* **索引过慢** → increase `MAX_WORKERS`, exclude large binaries via `.gitignore`
* **维度不符** → ensure both services use same `SentenceTransformer` model
* **GPU Faiss** → `poetry add faiss-gpu` then set `FAISS_USE_GPU=1`

## 📜 License
MIT

---

_最終更新 / Last Updated: 2025‑06‑02_
